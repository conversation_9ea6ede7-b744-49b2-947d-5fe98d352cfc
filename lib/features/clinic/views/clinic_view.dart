import 'package:dento_support/core/configs/colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ClinicView extends StatefulWidget {
  const ClinicView({
    super.key,
    required this.nameController,
    required this.mobileController,
    required this.locationController,
    this.onChanged,
    this.selectedItems = const [],
    this.onTimeScheduleChanged,
    this.onTimeSlotsChanged,
    this.isTimeScheduleEnabled = false,
    this.timeSlots = const [],
  });

  final TextEditingController nameController;
  final TextEditingController mobileController;
  final TextEditingController locationController;
  final ValueChanged<List<String>>? onChanged;
  final List<String> selectedItems;
  final ValueChanged<bool>? onTimeScheduleChanged;
  final ValueChanged<List<TimeSlot>>? onTimeSlotsChanged;
  final bool isTimeScheduleEnabled;
  final List<TimeSlot> timeSlots;

  @override
  State<ClinicView> createState() => _ClinicViewState();
}

class _ClinicViewState extends State<ClinicView> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ValueListenableBuilder(
          valueListenable: widget.nameController,
          builder: (context, value, child) {
            return TextFormField(
              controller: widget.nameController,
              textCapitalization: TextCapitalization.words,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp('[A-Za-z 0-9]'))
              ],
              decoration: InputDecoration(
                hintText: 'Clinic Name',
                hintStyle: AppFontStyle.hintStyle,
                contentPadding: EdgeInsets.only(
                    top: widget.nameController.text.isNotEmpty ? 15 : 0),
                suffixIcon: widget.nameController.text.isNotEmpty
                    ? IconButton(
                        icon: Container(
                          height: 20,
                          width: 20,
                          decoration: BoxDecoration(
                            color: const Color(0xFFDADADA),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.clear_rounded,
                              color: Colors.white,
                              size: 17,
                            ),
                          ),
                        ),
                        onPressed: () {
                          widget.nameController.clear();
                        },
                      )
                    : null,
              ),
              style: AppFontStyle.textStyle,
              validator: (value) {
                if (value!.isEmpty) {
                  return 'Please enter a clinic name';
                }
                return null;
              },
            );
          },
        ),
        const SizedBox(height: 30),
        Row(
          children: [
            const Expanded(
              child: TextField(
                readOnly: true,
                style: AppFontStyle.style2,
                decoration: InputDecoration(
                  hintText: '+91',
                  hintStyle: AppFontStyle.style2,
                  helperText: ' ',
                ),
              ),
            ),
            const SizedBox(width: 40),
            Expanded(
              flex: 5,
              child: TextFormField(
                controller: widget.mobileController,
                style: AppFontStyle.style2,
                keyboardType: TextInputType.phone,
                maxLength: 10,
                decoration: const InputDecoration(
                  hintText: 'Phone Number',
                  hintStyle: AppFontStyle.hintStyle,
                  counterText: '',
                  helperText: ' ',
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp('[0-9]'))
                ],
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Please enter a valid phone number';
                  } else if (value.length != 10) {
                    return 'Please enter a valid phone number';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 25),
        ValueListenableBuilder(
          valueListenable: widget.locationController,
          builder: (context, value, child) {
            return TextFormField(
              controller: widget.locationController,
              textCapitalization: TextCapitalization.words,
              decoration: InputDecoration(
                hintText: 'Clinic Location',
                hintStyle: AppFontStyle.hintStyle,
                contentPadding: EdgeInsets.only(
                    top: widget.locationController.text.isNotEmpty ? 15 : 0),
                suffixIcon: widget.locationController.text.isNotEmpty
                    ? IconButton(
                        icon: Container(
                          height: 20,
                          width: 20,
                          decoration: BoxDecoration(
                            color: const Color(0xFFDADADA),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.clear_rounded,
                              color: Colors.white,
                              size: 17,
                            ),
                          ),
                        ),
                        onPressed: () {
                          widget.locationController.clear();
                        },
                      )
                    : null,
              ),
              style: AppFontStyle.textStyle,
              validator: (value) {
                if (value!.isEmpty) {
                  return 'Please enter a clinic location';
                }
                return null;
              },
            );
          },
        ),
        const SizedBox(height: 30),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Schedule By Time",
              style: AppFontStyle.style6.copyWith(
                  color: AppColor.textColor,
                  fontFamily: 'assets/fonts/Inter-Regular.ttf',
                  fontWeight: FontWeight.w400),
            ),
            CupertinoSwitch(
              value: widget.isTimeScheduleEnabled,
              onChanged: (value) {
                widget.onTimeScheduleChanged?.call(value);
              },
              activeColor: AppColor.primaryColor,
            ),
          ],
        ),
        const SizedBox(height: 20),
        if (widget.isTimeScheduleEnabled) ...[
          ClinicTimingWidget(
            timeSlots: widget.timeSlots,
            onTimeSlotsChanged: widget.onTimeSlotsChanged,
          ),
          const SizedBox(height: 30),
        ],
        Center(
          child: ClinicOffDaySelection(
            onChanged: widget.onChanged,
            selectedItems: widget.selectedItems,
          ),
        ),
      ],
    );
  }
}

class ClinicOffDaySelection extends StatefulWidget {
  const ClinicOffDaySelection({
    super.key,
    this.onChanged,
    this.selectedItems = const [],
  });

  final List<String> selectedItems;
  final ValueChanged<List<String>>? onChanged;

  @override
  State<ClinicOffDaySelection> createState() => _ClinicOffDaySelectionState();
}

class _ClinicOffDaySelectionState extends State<ClinicOffDaySelection> {
  final days = [
    TestDays(initial: 'M', name: 'Monday'),
    TestDays(initial: 'T', name: 'Tuesday'),
    TestDays(initial: 'W', name: 'Wednesday'),
    TestDays(initial: 'T', name: 'Thursday'),
    TestDays(initial: 'F', name: 'Friday'),
    TestDays(initial: 'S', name: 'Saturday'),
    TestDays(initial: 'S', name: 'Sunday'),
  ];
  final localSelectedItems = <String>[];

  @override
  void initState() {
    for (final element in days) {
      if (widget.selectedItems.contains(element.name)) {
        element.selected = true;
        localSelectedItems.add(element.name);
      }
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Clinic’s Day Off',
          style: AppFontStyle.textStyle,
        ),
        const SizedBox(height: 13),
        Wrap(
          spacing: 14,
          runSpacing: 12,
          alignment: WrapAlignment.center,
          children: days.map(_buildChip).toList(),
        ),
      ],
    );
  }

  Widget _buildChip(TestDays e) {
    return InkWell(
      onTap:
          localSelectedItems.length == 6 && !localSelectedItems.contains(e.name)
              ? null
              : () {
                  final index =
                      days.indexWhere((element) => element.name == e.name);

                  setState(() {
                    if (days[index].selected) {
                      days[index].selected = false;
                      localSelectedItems.remove(e.name);
                    } else {
                      days[index].selected = true;
                      localSelectedItems.add(e.name);
                    }
                  });

                  widget.onChanged?.call(localSelectedItems);
                },
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: e.selected ? AppColor.primaryColor : const Color(0xFFDEDEDE),
          borderRadius: const BorderRadius.all(Radius.circular(18)),
        ),
        child: Center(
          child: Text(
            e.initial,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: AppFont.inter,
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: e.selected ? AppColor.backgroundColor : AppColor.textColor,
              letterSpacing: 1,
            ),
          ),
        ),
      ),
    );
  }
}

class TestDays {
  TestDays({required this.initial, required this.name, this.selected = false});

  String initial;
  String name;
  bool selected;
}

class TimeSlot {
  TimeSlot({
    required this.startTime,
    required this.endTime,
    required this.slotNumber,
  });

  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final int slotNumber;

  String get displayText {
    final hour1 = startTime.hourOfPeriod == 0 ? 12 : startTime.hourOfPeriod;
    final minute1 = startTime.minute.toString().padLeft(2, '0');
    final period1 = startTime.period == DayPeriod.am ? 'AM' : 'PM';
    final start = '$hour1:$minute1 $period1';

    final hour2 = endTime.hourOfPeriod == 0 ? 12 : endTime.hourOfPeriod;
    final minute2 = endTime.minute.toString().padLeft(2, '0');
    final period2 = endTime.period == DayPeriod.am ? 'AM' : 'PM';
    final end = '$hour2:$minute2 $period2';

    return '$start to $end';
  }

  TimeSlot copyWith({
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    int? slotNumber,
  }) {
    return TimeSlot(
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      slotNumber: slotNumber ?? this.slotNumber,
    );
  }
}

class ClinicTimingWidget extends StatefulWidget {
  const ClinicTimingWidget({
    super.key,
    required this.timeSlots,
    this.onTimeSlotsChanged,
  });

  final List<TimeSlot> timeSlots;
  final ValueChanged<List<TimeSlot>>? onTimeSlotsChanged;

  @override
  State<ClinicTimingWidget> createState() => _ClinicTimingWidgetState();
}

class _ClinicTimingWidgetState extends State<ClinicTimingWidget> {
  List<TimeSlot> _timeSlots = [];

  @override
  void initState() {
    super.initState();
    _timeSlots = List.from(widget.timeSlots);
    if (_timeSlots.isEmpty) {
      _addDefaultTimeSlot();
    }
  }

  void _addDefaultTimeSlot() {
    final now = DateTime.now();
    final currentTime = TimeOfDay.fromDateTime(now);
    final endTime = TimeOfDay(
      hour: (currentTime.hour + 2) % 24,
      minute: currentTime.minute,
    );

    final newSlot = TimeSlot(
      startTime: currentTime,
      endTime: endTime,
      slotNumber: _timeSlots.length + 1,
    );
    _timeSlots.add(newSlot);
    widget.onTimeSlotsChanged?.call(_timeSlots);
  }

  void _addTimeSlot() {
    final now = DateTime.now();
    final currentTime = TimeOfDay.fromDateTime(now);
    final endTime = TimeOfDay(
      hour: (currentTime.hour + 2) % 24,
      minute: currentTime.minute,
    );

    final newSlot = TimeSlot(
      startTime: currentTime,
      endTime: endTime,
      slotNumber: _timeSlots.length + 1,
    );
    setState(() {
      _timeSlots.add(newSlot);
    });
    widget.onTimeSlotsChanged?.call(_timeSlots);
  }

  void _removeTimeSlot(int index) {
    if (_timeSlots.length > 1) {
      setState(() {
        _timeSlots.removeAt(index);
        // Update slot numbers
        for (int i = 0; i < _timeSlots.length; i++) {
          _timeSlots[i] = _timeSlots[i].copyWith(slotNumber: i + 1);
        }
      });
      widget.onTimeSlotsChanged?.call(_timeSlots);
    }
  }

  Future<void> _selectTime(int index, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime
          ? _timeSlots[index].startTime
          : _timeSlots[index].endTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColor.primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _timeSlots[index] = _timeSlots[index].copyWith(startTime: picked);
        } else {
          _timeSlots[index] = _timeSlots[index].copyWith(endTime: picked);
        }
      });
      widget.onTimeSlotsChanged?.call(_timeSlots);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Clinic Timing',
          style: AppFontStyle.textStyle,
        ),
        const SizedBox(height: 15),
        ..._timeSlots.asMap().entries.map((entry) {
          final index = entry.key;
          final slot = entry.value;
          return _buildTimeSlotRow(slot, index);
        }).toList(),
        const SizedBox(height: 15),
        _buildAddTimeSlotButton(),
      ],
    );
  }

  Widget _buildTimeSlotRow(TimeSlot slot, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Row(
        children: [
          // Start Time Field
          Expanded(
            flex: 2,
            child: GestureDetector(
              onTap: () => _selectTime(index, true),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: const Color(0xFFDEE2E6)),
                ),
                child: Text(
                  _formatTimeOfDay(slot.startTime),
                  style: AppFontStyle.textStyle.copyWith(fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          const Text(
            'to',
            style: TextStyle(
              fontFamily: AppFont.inter,
              fontWeight: FontWeight.w500,
              fontSize: 12,
              color: AppColor.subtitleColor,
            ),
          ),
          const SizedBox(width: 8),
          // End Time Field
          Expanded(
            flex: 2,
            child: GestureDetector(
              onTap: () => _selectTime(index, false),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: const Color(0xFFDEE2E6)),
                ),
                child: Text(
                  _formatTimeOfDay(slot.endTime),
                  style: AppFontStyle.textStyle.copyWith(fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'Slot ${slot.slotNumber}',
            style: AppFontStyle.textStyle.copyWith(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: AppColor.primaryColor,
            ),
          ),
          if (_timeSlots.length > 1) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () => _removeTimeSlot(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColor.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.close,
                  size: 16,
                  color: AppColor.red,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  Widget _buildAddTimeSlotButton() {
    return GestureDetector(
      onTap: _addTimeSlot,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: AppColor.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColor.primaryColor.withOpacity(0.3),
            style: BorderStyle.solid,
          ),
        ),
        child: const Center(
          child: Text(
            'Add Time Slot',
            style: TextStyle(
              fontFamily: AppFont.inter,
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: AppColor.primaryColor,
            ),
          ),
        ),
      ),
    );
  }
}
