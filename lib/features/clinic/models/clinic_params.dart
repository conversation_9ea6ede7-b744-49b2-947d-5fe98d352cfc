import 'package:dento_support/features/clinic/models/time_slot.dart';
import 'package:flutter/material.dart';

class ClinicParams {
  const ClinicParams({
    required this.name,
    required this.mobile,
    required this.location,
    required this.dayOff,
    this.scheduleByTime = false,
    this.timeSlots = const [],
  });

  final String name;
  final String mobile;
  final String location;
  final String dayOff;
  final bool scheduleByTime;
  final List<TimeSlot> timeSlots;

  Map<String, dynamic> toJson() {
    final json = {
      'name': name,
      'mobile': mobile,
      'location': location,
      'dayOff': dayOff,
      'scheduleByTime': scheduleByTime,
    };

    if (scheduleByTime && timeSlots.isNotEmpty) {
      final completeSlots = timeSlots.where((slot) => slot.isComplete).toList();

      if (completeSlots.length == 1) {
        // Single slot: use array format
        json['timeRanges'] = [
          {
            'start': _formatTimeForApi(completeSlots[0].startTime!),
            'end': _formatTimeForApi(completeSlots[0].endTime!),
          }
        ];
      } else if (completeSlots.length > 1) {
        // Multiple slots: use comma-separated string format
        json['timeRanges'] = completeSlots
            .map((slot) => '${_formatTimeForApi(slot.startTime!)}-${_formatTimeForApi(slot.endTime!)}')
            .join(',');
      }
    }

    return json;
  }

  String _formatTimeForApi(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
